spring.application.name=${SPRING_APP_NAME:eazybankbackend}
logging.level.org.springframework.security=${SPRING_SECURITY_LOG_LEVEL:TRACE}

spring.config.import = application_prod.properties
spring.profiles.active = default

spring.datasource.url=jdbc:mysql://${DATABASE_HOST:localhost}:${DATABASE_PORT:3306}/${DATABASE_NAME:eazybank}
spring.datasource.username=${DATABASE_USERNAME:root}
spring.datasource.password=${DATABASE_PASSWORD:root}
spring.jpa.show-sql=${JPA_SHOW_SQL:true}
spring.jpa.properties.hibernate.format_sql=${HIBERNATE_FORMAT_SQL:true}

logging.pattern.console = ${LOGPATTERN_CONSOLE:%green(%d{HH:mm:ss.SSS}) %blue(%-5level) %red([%thread]) %yellow(%logger{15}) - %msg%n}

server.servlet.session.timeout=${SESSION_TIMEOUT:20m}
