package com.eazybytes.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    @GetMapping("/testadmin")
    @PreAuthorize("hasRole('SYSTEM')")
    public String testAdmin() {
        return "admin success";
    }

    @GetMapping("/testemployee")
    @PreAuthorize("hasRole('EMPLOYEE')")
    public String testEmployee() {
        return "employee success";
    }
}